"""
Unit tests for ExcelStreamingWriter.

This module tests the ExcelStreamingWriter class functionality including
both vertical and horizontal layouts, period separation, and error handling.
"""

import asyncio
import os
import tempfile
import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from magic_gateway.export.exceptions import ExcelLimitExceededError, FormatConversionError
from magic_gateway.export.formats.excel_streamer import ExcelStreamingWriter
from magic_gateway.export.models import ExcelLayout, JobMetadata


class TestExcelStreamingWriter:
    """Test cases for ExcelStreamingWriter."""
    
    @pytest.fixture
    def mock_connection_manager(self):
        """Create a mock connection manager."""
        manager = MagicMock()
        manager.initialized = True
        return manager
    
    @pytest.fixture
    def sample_job_metadata(self):
        """Create sample job metadata."""
        return JobMetadata(
            job_id=12345,
            table_name="test_table",
            database_name="test_db",
            total_rows=1000,
            available_periods=["2023-01", "2023-02"],
            facts_list=["revenue", "cost", "profit"],
            axes_info={"axis_1": "region", "axis_2": "product"},
            estimated_size_mb=5.0,
            created_at=None
        )
    
    def test_excel_streaming_writer_initialization(self):
        """Test ExcelStreamingWriter initialization."""
        writer = ExcelStreamingWriter()
        
        assert writer.workbook is None
        assert writer.worksheets == {}
        assert writer.current_rows == {}
        assert writer.sheet_counter == {}
        assert writer.EXCEL_MAX_ROWS == 1048576
        assert writer.EXCEL_MAX_COLS == 16384
    
    def test_clean_excel_value(self):
        """Test Excel value cleaning functionality."""
        writer = ExcelStreamingWriter()
        
        # Test None values
        assert writer._clean_excel_value(None) == ""
        
        # Test normal values
        assert writer._clean_excel_value("test") == "test"
        assert writer._clean_excel_value(123) == 123
        
        # Test null/na string values
        assert writer._clean_excel_value("null") == ""
        assert writer._clean_excel_value("NULL") == ""
        assert writer._clean_excel_value("na") == ""
        assert writer._clean_excel_value("nan") == ""
        assert writer._clean_excel_value("none") == ""
        assert writer._clean_excel_value("") == ""
    
    def test_sanitize_sheet_name(self):
        """Test sheet name sanitization."""
        writer = ExcelStreamingWriter()
        
        # Test invalid characters
        assert writer._sanitize_sheet_name("test/sheet") == "test_sheet"
        assert writer._sanitize_sheet_name("test\\sheet") == "test_sheet"
        assert writer._sanitize_sheet_name("test*sheet") == "test_sheet"
        assert writer._sanitize_sheet_name("test[sheet]") == "test_sheet_"
        assert writer._sanitize_sheet_name("test:sheet") == "test_sheet"
        assert writer._sanitize_sheet_name("test?sheet") == "test_sheet"
        
        # Test length limit (31 characters)
        long_name = "a" * 40
        sanitized = writer._sanitize_sheet_name(long_name)
        assert len(sanitized) == 31
        assert sanitized == "a" * 31
    
    def test_add_period_filter(self):
        """Test period filter addition to queries."""
        writer = ExcelStreamingWriter()
        
        # Test query without WHERE clause
        query = "SELECT * FROM table"
        filtered = writer._add_period_filter(query, "2023-01")
        assert filtered == "SELECT * FROM table WHERE period_name = '2023-01'"
        
        # Test query with existing WHERE clause
        query = "SELECT * FROM table WHERE job_id = 123"
        filtered = writer._add_period_filter(query, "2023-01")
        assert filtered == "SELECT * FROM table WHERE job_id = 123 AND period_name = '2023-01'"
        
        # Test period with quotes (should be escaped)
        query = "SELECT * FROM table"
        filtered = writer._add_period_filter(query, "test'period")
        assert filtered == "SELECT * FROM table WHERE period_name = 'test'period'"
    
    def test_get_default_columns(self):
        """Test default column generation."""
        writer = ExcelStreamingWriter()
        
        # Test zero columns
        assert writer._get_default_columns(0) == []
        
        # Test normal number of columns
        columns = writer._get_default_columns(5)
        assert len(columns) == 5
        assert columns == ['job_id', 'period_name', 'axis_1', 'axis_2', 'axis_3']
        
        # Test more columns than defaults
        columns = writer._get_default_columns(20)
        assert len(columns) == 20
        assert columns[0] == 'job_id'
        assert columns[-1] == 'column_20'
    
    @pytest.mark.asyncio
    async def test_validate_excel_limits_success(self, mock_connection_manager, sample_job_metadata):
        """Test successful Excel limits validation."""
        writer = ExcelStreamingWriter()
        
        # Mock error context
        with patch('magic_gateway.export.formats.excel_streamer.ExportErrorContext') as mock_ctx:
            mock_ctx_instance = MagicMock()
            mock_ctx_instance.request_id = "test-request"
            mock_ctx_instance.get_context.return_value = {}
            mock_ctx.return_value.__enter__.return_value = mock_ctx_instance
            
            # Should not raise for small dataset
            await writer._validate_excel_limits(
                sample_job_metadata, ExcelLayout.VERTICAL, False, mock_ctx_instance
            )
    
    @pytest.mark.asyncio
    async def test_validate_excel_limits_row_limit_exceeded(self, mock_connection_manager, sample_job_metadata):
        """Test Excel row limit validation failure."""
        writer = ExcelStreamingWriter()
        
        # Create metadata with too many rows
        large_metadata = JobMetadata(
            job_id=12345,
            table_name="test_table",
            database_name="test_db",
            total_rows=2000000,  # Exceeds Excel limit
            available_periods=["2023-01"],
            facts_list=["revenue"],
            axes_info={"axis_1": "region"},
            estimated_size_mb=50.0,
            created_at=None
        )
        
        # Mock error context
        with patch('magic_gateway.export.formats.excel_streamer.ExportErrorContext') as mock_ctx:
            mock_ctx_instance = MagicMock()
            mock_ctx_instance.request_id = "test-request"
            mock_ctx_instance.get_context.return_value = {}
            mock_ctx.return_value.__enter__.return_value = mock_ctx_instance
            
            with pytest.raises(ExcelLimitExceededError):
                await writer._validate_excel_limits(
                    large_metadata, ExcelLayout.VERTICAL, False, mock_ctx_instance
                )
    
    @pytest.mark.asyncio
    async def test_validate_excel_limits_column_limit_exceeded(self, mock_connection_manager, sample_job_metadata):
        """Test Excel column limit validation failure."""
        writer = ExcelStreamingWriter()
        
        # Create metadata with too many facts (columns)
        many_facts_metadata = JobMetadata(
            job_id=12345,
            table_name="test_table",
            database_name="test_db",
            total_rows=1000,
            available_periods=["2023-01"],
            facts_list=[f"fact_{i}" for i in range(20000)],  # Too many facts
            axes_info={f"axis_{i}": f"axis_{i}" for i in range(100)},  # Many axes
            estimated_size_mb=5.0,
            created_at=None
        )
        
        # Mock error context
        with patch('magic_gateway.export.formats.excel_streamer.ExportErrorContext') as mock_ctx:
            mock_ctx_instance = MagicMock()
            mock_ctx_instance.request_id = "test-request"
            mock_ctx_instance.get_context.return_value = {}
            mock_ctx.return_value.__enter__.return_value = mock_ctx_instance
            
            with pytest.raises(FormatConversionError):
                await writer._validate_excel_limits(
                    many_facts_metadata, ExcelLayout.HORIZONTAL, False, mock_ctx_instance
                )
    
    def test_create_worksheet_with_validation(self):
        """Test worksheet creation with name validation."""
        writer = ExcelStreamingWriter()
        
        # Mock workbook
        mock_workbook = MagicMock()
        mock_worksheet = MagicMock()
        mock_workbook.add_worksheet.return_value = mock_worksheet
        writer.workbook = mock_workbook
        
        # Test normal sheet creation
        worksheet = writer._create_worksheet_with_validation("Test Sheet")
        assert worksheet == mock_worksheet
        assert "Test Sheet" in writer.worksheets  # The method doesn't sanitize by default
        assert writer.current_rows["Test Sheet"] == 0
        
        # Test duplicate name handling
        worksheet2 = writer._create_worksheet_with_validation("Test Sheet")
        mock_workbook.add_worksheet.assert_called_with("Test Sheet_01")
    
    def test_get_sheet_summary(self):
        """Test sheet summary generation."""
        writer = ExcelStreamingWriter()
        
        # Set up some test data
        writer.worksheets = {"Sheet1": MagicMock(), "Sheet2": MagicMock()}
        writer.current_rows = {"Sheet1": 101, "Sheet2": 51}  # Including header rows
        
        summary = writer._get_sheet_summary()
        
        assert summary["total_sheets"] == 2
        assert summary["sheet_names"] == ["Sheet1", "Sheet2"]
        assert summary["total_rows_by_sheet"] == {"Sheet1": 101, "Sheet2": 51}
        assert summary["total_data_rows"] == 150  # 100 + 50 (excluding headers)