"""
Unit tests for the MetadataAnalyzer service.

This module tests the metadata analysis functionality including metadata retrieval,
size calculations, Excel limit validation, and chunking strategy determination.
"""

import json
import pytest
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

from magic_gateway.export.exceptions import (
    ExcelLimitExceededError,
    ExportError,
    MetadataRetrievalError,
)
from magic_gateway.export.models import (
    ExportFormat,
    ExportOptions,
    ExcelLayout,
    JobMetadata,
    OptimizationStrategy,
    SizeEstimate,
    TempFileStrategy,
)
from magic_gateway.export.services.metadata_analyzer import MetadataAnalyzer


@pytest.fixture
def mock_connection_manager():
    """Create a mock ClickHouse connection manager."""
    manager = MagicMock()
    manager.initialized = True
    return manager


@pytest.fixture
def mock_connection():
    """Create a mock database connection."""
    connection = MagicMock()
    return connection


@pytest.fixture
def sample_metadata():
    """Create sample job metadata for testing."""
    return JobMetadata(
        job_id=12345,
        table_name="test_results",
        database_name="test_db",
        total_rows=500000,
        available_periods=["2024-01", "2024-02", "2024-03"],
        facts_list=["revenue", "cost", "profit", "units"],
        axes_info={"dimensions": ["region", "product"], "measures": 4},
        estimated_size_mb=125.5,
        created_at=datetime(2024, 1, 15, 10, 30, 0),
    )


@pytest.fixture
def metadata_analyzer(mock_connection_manager):
    """Create a MetadataAnalyzer instance with mocked dependencies."""
    return MetadataAnalyzer(mock_connection_manager)


class TestMetadataAnalyzer:
    """Test cases for the MetadataAnalyzer class."""

    @pytest.mark.asyncio
    async def test_analyze_job_metadata_success(
        self, metadata_analyzer, mock_connection_manager, mock_connection
    ):
        """Test successful metadata retrieval and parsing."""
        # Setup mock data
        mock_row = [
            12345,  # job_id
            "test_results",  # table_name
            "test_db",  # database_name
            500000,  # total_rows
            '["2024-01", "2024-02", "2024-03"]',  # available_periods (JSON)
            '["revenue", "cost", "profit", "units"]',  # facts_list (JSON)
            '{"dimensions": ["region", "product"], "measures": 4}',  # axes_info (JSON)
            125.5,  # estimated_size_mb
            datetime(2024, 1, 15, 10, 30, 0),  # created_at
        ]

        mock_connection.execute.return_value = [mock_row]
        mock_connection_manager.connection.return_value.__aenter__.return_value = (
            mock_connection
        )

        # Execute the method
        result = await metadata_analyzer.analyze_job_metadata(12345, "test-request-123")

        # Verify the result
        assert isinstance(result, JobMetadata)
        assert result.job_id == 12345
        assert result.table_name == "test_results"
        assert result.database_name == "test_db"
        assert result.total_rows == 500000
        assert result.available_periods == ["2024-01", "2024-02", "2024-03"]
        assert result.facts_list == ["revenue", "cost", "profit", "units"]
        assert result.axes_info == {"dimensions": ["region", "product"], "measures": 4}
        assert result.estimated_size_mb == 125.5
        assert result.created_at == datetime(2024, 1, 15, 10, 30, 0)

        # Verify the query was executed correctly
        mock_connection.execute.assert_called_once()
        call_args = mock_connection.execute.call_args
        assert "SELECT" in call_args[0][0]
        assert "job_metadata" in call_args[0][0]
        assert call_args[0][1]["job_id"] == 12345

    @pytest.mark.asyncio
    async def test_analyze_job_metadata_not_found(
        self, metadata_analyzer, mock_connection_manager, mock_connection
    ):
        """Test metadata retrieval when job is not found."""
        mock_connection.execute.return_value = []
        mock_connection_manager.connection.return_value.__aenter__.return_value = (
            mock_connection
        )

        with pytest.raises(MetadataRetrievalError) as exc_info:
            await metadata_analyzer.analyze_job_metadata(99999, "test-request-123")

        assert exc_info.value.error_type == "metadata_retrieval_error"
        assert "No metadata found" in exc_info.value.message
        assert exc_info.value.context["job_id"] == 99999

    @pytest.mark.asyncio
    async def test_analyze_job_metadata_connection_not_initialized(
        self, metadata_analyzer, mock_connection_manager
    ):
        """Test metadata retrieval when connection manager is not initialized."""
        mock_connection_manager.initialized = False

        with pytest.raises(ExportError) as exc_info:
            await metadata_analyzer.analyze_job_metadata(12345, "test-request-123")

        assert exc_info.value.error_type == "connection_not_initialized"
        assert "not initialized" in exc_info.value.message

    def test_parse_periods_field_json_array(self, metadata_analyzer):
        """Test parsing periods field from JSON array."""
        periods_data = '["2024-01", "2024-02", "2024-03"]'
        result = metadata_analyzer._parse_periods_field(periods_data)
        assert result == ["2024-01", "2024-02", "2024-03"]

    def test_parse_periods_field_comma_separated(self, metadata_analyzer):
        """Test parsing periods field from comma-separated string."""
        periods_data = "2024-01, 2024-02, 2024-03"
        result = metadata_analyzer._parse_periods_field(periods_data)
        assert result == ["2024-01", "2024-02", "2024-03"]

    def test_parse_periods_field_list(self, metadata_analyzer):
        """Test parsing periods field from Python list."""
        periods_data = ["2024-01", "2024-02", "2024-03"]
        result = metadata_analyzer._parse_periods_field(periods_data)
        assert result == ["2024-01", "2024-02", "2024-03"]

    def test_parse_facts_field_json_array(self, metadata_analyzer):
        """Test parsing facts field from JSON array."""
        facts_data = '["revenue", "cost", "profit"]'
        result = metadata_analyzer._parse_facts_field(facts_data)
        assert result == ["revenue", "cost", "profit"]

    def test_parse_axes_field_json_object(self, metadata_analyzer):
        """Test parsing axes field from JSON object."""
        axes_data = '{"dimensions": ["region", "product"], "measures": 4}'
        result = metadata_analyzer._parse_axes_field(axes_data)
        assert result == {"dimensions": ["region", "product"], "measures": 4}

    def test_parse_axes_field_dict(self, metadata_analyzer):
        """Test parsing axes field from Python dict."""
        axes_data = {"dimensions": ["region", "product"], "measures": 4}
        result = metadata_analyzer._parse_axes_field(axes_data)
        assert result == {"dimensions": ["region", "product"], "measures": 4}

    @pytest.mark.asyncio
    async def test_calculate_export_size_estimates(
        self, metadata_analyzer, sample_metadata
    ):
        """Test export size calculation for different formats."""
        result = await metadata_analyzer.calculate_export_size_estimates(
            sample_metadata, ExportFormat.CSV, "test-request-123"
        )

        assert isinstance(result, SizeEstimate)
        assert result.csv_size_mb > 0
        assert result.excel_vertical_size_mb > 0
        assert result.excel_horizontal_size_mb > 0
        assert result.parquet_size_mb > 0
        assert len(result.rows_per_period) == 3  # Three periods in sample data

        # Verify relative sizes make sense
        assert (
            result.parquet_size_mb < result.csv_size_mb
        )  # Parquet should be smaller due to compression
        assert (
            result.excel_vertical_size_mb > result.csv_size_mb
        )  # Excel has more overhead

    def test_estimate_bytes_per_row(self, metadata_analyzer, sample_metadata):
        """Test bytes per row estimation."""
        result = metadata_analyzer._estimate_bytes_per_row(sample_metadata)

        assert isinstance(result, int)
        assert result > 0
        # Should be reasonable for a row with 4 facts and typical dimensions
        assert 50 < result < 500

    def test_calculate_csv_size(self, metadata_analyzer):
        """Test CSV size calculation."""
        result = metadata_analyzer._calculate_csv_size(100000, 100)

        assert result > 0
        # Should be approximately 100K rows * 100 bytes * 1.1 overhead / (1024*1024)
        expected_mb = (100000 * 100 * 1.1) / (1024 * 1024)
        assert abs(result - expected_mb) < 1.0  # Within 1MB tolerance

    def test_calculate_parquet_size(self, metadata_analyzer):
        """Test Parquet size calculation."""
        result = metadata_analyzer._calculate_parquet_size(100000, 100)

        assert result > 0
        # Parquet should be significantly smaller due to compression
        csv_size = metadata_analyzer._calculate_csv_size(100000, 100)
        assert result < csv_size

    @pytest.mark.asyncio
    async def test_validate_excel_limits_within_limits(
        self, metadata_analyzer, sample_metadata
    ):
        """Test Excel limits validation when within limits."""
        options = ExportOptions(
            format=ExportFormat.EXCEL, separate_periods=False, horizontal_facts=False
        )

        result = await metadata_analyzer.validate_excel_limits(
            sample_metadata, options, "test-request-123"
        )

        assert result is True

    @pytest.mark.asyncio
    async def test_validate_excel_limits_exceeded(self, metadata_analyzer):
        """Test Excel limits validation when limits are exceeded."""
        # Create metadata with too many rows
        large_metadata = JobMetadata(
            job_id=12345,
            table_name="large_results",
            database_name="test_db",
            total_rows=2000000,  # Exceeds Excel limit
            available_periods=["2024-01"],
            facts_list=["revenue"],
            axes_info={},
            estimated_size_mb=500.0,
            created_at=datetime.now(),
        )

        options = ExportOptions(
            format=ExportFormat.EXCEL, separate_periods=False, horizontal_facts=False
        )

        with pytest.raises(ExcelLimitExceededError) as exc_info:
            await metadata_analyzer.validate_excel_limits(
                large_metadata, options, "test-request-123"
            )

        assert exc_info.value.error_type == "export_validation_error"
        assert "exceeding Excel limit" in exc_info.value.message
        # Check the context fields that are actually present
        assert exc_info.value.context["original_rows"] == 2000000
        assert exc_info.value.context["max_rows_per_sheet"] == 2000000
        assert exc_info.value.context["job_id"] == 12345

    @pytest.mark.asyncio
    async def test_validate_excel_limits_horizontal_layout(self, metadata_analyzer):
        """Test Excel limits validation with horizontal layout."""
        # Create metadata that would exceed limits in vertical but not horizontal
        metadata = JobMetadata(
            job_id=12345,
            table_name="test_results",
            database_name="test_db",
            total_rows=2000000,  # Would exceed in vertical
            available_periods=["2024-01"],
            facts_list=["fact1", "fact2", "fact3", "fact4"],  # 4 facts
            axes_info={},
            estimated_size_mb=500.0,
            created_at=datetime.now(),
        )

        options = ExportOptions(
            format=ExportFormat.EXCEL,
            separate_periods=False,
            horizontal_facts=True,  # This should reduce rows
        )

        # Should pass because horizontal layout reduces rows by facts count
        result = await metadata_analyzer.validate_excel_limits(
            metadata, options, "test-request-123"
        )

        assert result is True

    @pytest.mark.asyncio
    async def test_determine_chunking_strategy_small_dataset(self, metadata_analyzer):
        """Test chunking strategy for small dataset."""
        small_metadata = JobMetadata(
            job_id=12345,
            table_name="small_results",
            database_name="test_db",
            total_rows=50000,  # Small dataset
            available_periods=["2024-01"],
            facts_list=["revenue"],
            axes_info={},
            estimated_size_mb=10.0,
            created_at=datetime.now(),
        )

        result = await metadata_analyzer.determine_chunking_strategy(
            small_metadata, "test-request-123"
        )

        assert isinstance(result, OptimizationStrategy)
        assert result.use_streaming is False  # Small datasets don't need streaming
        assert result.chunk_size == 50000  # Should use total_rows for small datasets
        assert result.period_chunking is False
        assert result.memory_limit_mb == 256
        assert result.temp_file_strategy == TempFileStrategy.MEMORY_ONLY

    @pytest.mark.asyncio
    async def test_determine_chunking_strategy_large_dataset(self, metadata_analyzer):
        """Test chunking strategy for large dataset."""
        large_metadata = JobMetadata(
            job_id=12345,
            table_name="large_results",
            database_name="test_db",
            total_rows=5000000,  # Large dataset
            available_periods=["2024-01", "2024-02", "2024-03", "2024-04"],
            facts_list=["revenue", "cost"],
            axes_info={},
            estimated_size_mb=1000.0,
            created_at=datetime.now(),
        )

        result = await metadata_analyzer.determine_chunking_strategy(
            large_metadata, "test-request-123"
        )

        assert isinstance(result, OptimizationStrategy)
        assert result.use_streaming is True  # Large datasets need streaming
        assert result.chunk_size == 100000
        assert result.period_chunking is True  # Multiple periods enable period chunking
        assert result.memory_limit_mb == 1024
        assert result.temp_file_strategy == TempFileStrategy.PERIOD_CHUNKS

    @pytest.mark.asyncio
    async def test_determine_chunking_strategy_very_large_dataset(
        self, metadata_analyzer
    ):
        """Test chunking strategy for very large dataset."""
        very_large_metadata = JobMetadata(
            job_id=12345,
            table_name="very_large_results",
            database_name="test_db",
            total_rows=15000000,  # Very large dataset
            available_periods=["2024-01"],  # Single period
            facts_list=["revenue"],
            axes_info={},
            estimated_size_mb=3000.0,
            created_at=datetime.now(),
        )

        result = await metadata_analyzer.determine_chunking_strategy(
            very_large_metadata, "test-request-123"
        )

        assert isinstance(result, OptimizationStrategy)
        assert result.use_streaming is True
        assert result.chunk_size == 1000000  # 1M rows for very large datasets
        assert result.period_chunking is False  # Single period, no period chunking
        assert result.memory_limit_mb == 10240  # 10GB memory limit
        assert result.temp_file_strategy == TempFileStrategy.SINGLE_TEMP
